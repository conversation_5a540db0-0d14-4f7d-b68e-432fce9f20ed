#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析功能
"""

import json
from log_importer import LogImporter

def test_json_parsing():
    """测试各种JSON解析情况"""
    
    # 创建导入器实例（仅用于测试JSON解析）
    importer = LogImporter(database_name="test_db")
    
    # 测试用例
    test_cases = [
        # 1. 单个简单JSON
        {
            "name": "单个简单JSON",
            "input": '{"t_log_id":"123","t_event_name":"test","value":1}',
            "expected_count": 1
        },
        
        # 2. 单个复杂嵌套JSON
        {
            "name": "单个复杂嵌套JSON",
            "input": '{"t_log_id":"456","t_event_name":"complex","data":{"nested":{"deep":{"value":42}},"array":[1,2,3]}}',
            "expected_count": 1
        },
        
        # 3. 两个连续的JSON对象
        {
            "name": "两个连续JSON",
            "input": '{"t_log_id":"789","t_event_name":"first"}{"t_log_id":"790","t_event_name":"second"}',
            "expected_count": 2
        },
        
        # 4. 带有嵌套对象的多个JSON
        {
            "name": "多个复杂JSON",
            "input": '{"t_log_id":"001","data":{"nested":{"value":1}}}{"t_log_id":"002","data":{"nested":{"value":2}}}',
            "expected_count": 2
        },
        
        # 5. 带有数组的JSON
        {
            "name": "包含数组的JSON",
            "input": '{"t_log_id":"003","items":[{"id":1,"name":"item1"},{"id":2,"name":"item2"}]}',
            "expected_count": 1
        },
        
        # 6. 多个JSON，其中包含数组和嵌套对象
        {
            "name": "复杂多JSON",
            "input": '{"t_log_id":"004","config":{"settings":{"debug":true},"items":[1,2,3]}}{"t_log_id":"005","simple":"value"}',
            "expected_count": 2
        },
        
        # 7. 带有前缀文本的JSON
        {
            "name": "带前缀的JSON",
            "input": 'some prefix text {"t_log_id":"006","t_event_name":"prefixed"}',
            "expected_count": 1
        },
        
        # 8. 多个JSON之间有空格
        {
            "name": "JSON间有空格",
            "input": '{"t_log_id":"007","value":1}   {"t_log_id":"008","value":2}',
            "expected_count": 2
        },
        
        # 9. 空行
        {
            "name": "空行",
            "input": '',
            "expected_count": 0
        },
        
        # 10. 只有空格的行
        {
            "name": "空格行",
            "input": '   ',
            "expected_count": 0
        },
        
        # 11. 无效JSON
        {
            "name": "无效JSON",
            "input": '{"invalid": json}',
            "expected_count": 0
        },
        
        # 12. 实际日志样例
        {
            "name": "实际日志样例",
            "input": '{"t_log_id":"24e4ba1753e5469f86c29ee0407c961420250101010009","t_utc_timestamp":"2025-01-01T01:00:09.420493067+08:00","t_event_name":"online_num","property":{"timestamp":1735664409,"log_id":"24e4ba1753e5469f86c29ee0407c961420250101010009","online_num":5}}',
            "expected_count": 1
        }
    ]
    
    print("=== JSON解析功能测试 ===\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_case['name']}")
        print(f"输入: {test_case['input'][:100]}{'...' if len(test_case['input']) > 100 else ''}")
        
        try:
            result = importer._parse_json_line(test_case['input'])
            actual_count = len(result)
            expected_count = test_case['expected_count']
            
            if actual_count == expected_count:
                print(f"✓ 通过: 解析出 {actual_count} 个JSON对象")
                passed += 1
                
                # 显示解析结果的简要信息
                for j, obj in enumerate(result):
                    if isinstance(obj, dict):
                        log_id = obj.get('t_log_id', 'N/A')
                        event_name = obj.get('t_event_name', 'N/A')
                        print(f"  对象 {j+1}: t_log_id={log_id}, t_event_name={event_name}")
                    else:
                        print(f"  对象 {j+1}: {type(obj).__name__}")
            else:
                print(f"✗ 失败: 期望 {expected_count} 个，实际 {actual_count} 个")
                failed += 1
                
        except Exception as e:
            print(f"✗ 异常: {e}")
            failed += 1
        
        print("-" * 60)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    
    if failed == 0:
        print("🎉 所有测试通过！")
    else:
        print(f"⚠️  有 {failed} 个测试失败")
    
    importer.close()

def test_performance():
    """测试解析性能"""
    print("\n=== 性能测试 ===")
    
    import time
    
    importer = LogImporter(database_name="test_db")
    
    # 创建测试数据
    single_json = '{"t_log_id":"perf_test","t_event_name":"performance","data":{"nested":{"value":42},"array":[1,2,3,4,5]}}'
    multi_json = single_json + single_json + single_json  # 3个JSON对象
    
    test_cases = [
        ("单个JSON", single_json, 1000),
        ("多个JSON", multi_json, 1000)
    ]
    
    for name, test_data, iterations in test_cases:
        start_time = time.time()
        
        total_objects = 0
        for _ in range(iterations):
            result = importer._parse_json_line(test_data)
            total_objects += len(result)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        print(f"{name}:")
        print(f"  迭代次数: {iterations}")
        print(f"  总耗时: {elapsed:.3f}秒")
        print(f"  平均每次: {elapsed/iterations*1000:.3f}毫秒")
        print(f"  解析对象总数: {total_objects}")
        print(f"  每秒解析: {total_objects/elapsed:.0f}个对象")
        print()
    
    importer.close()

if __name__ == "__main__":
    test_json_parsing()
    test_performance()
