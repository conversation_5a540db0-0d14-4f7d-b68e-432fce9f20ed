#!/bin/bash

echo "安装MongoDB日志导入工具依赖..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

echo "安装Python依赖包..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "错误: 依赖安装失败"
    exit 1
fi

echo
echo "安装完成！"
echo
echo "使用方法:"
echo "  python3 run_import.py          - 导入所有日志文件"
echo "  python3 log_importer.py --help - 查看详细参数"
echo "  python3 test_current_log.py    - 测试当前目录中的日志文件"
echo
