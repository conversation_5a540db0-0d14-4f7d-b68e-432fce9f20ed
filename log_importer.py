#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB日志导入工具
用于将.action.log文件中的JSON数据导入到MongoDB中
根据t_event_name字段分表存储，支持断点续传和去重
"""

import os
import json
import logging
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

import pymongo
from pymongo import MongoClient, UpdateOne
from pymongo.errors import BulkWriteError


class LogImporter:
    """日志导入器"""
    
    def __init__(self, 
                 mongo_uri: str = "mongodb://localhost:27017",
                 database_name: str = "jkx_event_log",
                 log_dir: str = r"H:\jkxlogs",
                 batch_size: int = 1000):
        """
        初始化导入器
        
        Args:
            mongo_uri: MongoDB连接字符串
            database_name: 数据库名称
            log_dir: 日志文件目录
            batch_size: 批量插入大小
        """
        self.mongo_uri = mongo_uri
        self.database_name = database_name
        self.log_dir = Path(log_dir)
        self.batch_size = batch_size
        
        # 初始化MongoDB连接
        self.client = MongoClient(mongo_uri)
        self.db = self.client[database_name]
        
        # 导入状态记录表
        self.import_status_collection = self.db["_import_status"]
        
        # 设置日志
        self._setup_logging()
        
        # 初始化索引
        self._setup_indexes()
    
    def _setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('import.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _setup_indexes(self):
        """设置必要的索引"""
        try:
            # 为导入状态表创建索引
            self.import_status_collection.create_index("file_path", unique=True)
            self.import_status_collection.create_index("file_hash")
            self.logger.info("索引设置完成")
        except Exception as e:
            self.logger.error(f"设置索引失败: {e}")
    
    def _get_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _is_file_imported(self, file_path: Path) -> bool:
        """检查文件是否已经导入"""
        file_hash = self._get_file_hash(file_path)
        status = self.import_status_collection.find_one({
            "file_path": str(file_path),
            "file_hash": file_hash,
            "status": "completed"
        })
        return status is not None
    
    def _mark_file_importing(self, file_path: Path) -> str:
        """标记文件开始导入"""
        file_hash = self._get_file_hash(file_path)
        status_doc = {
            "file_path": str(file_path),
            "file_hash": file_hash,
            "status": "importing",
            "start_time": datetime.utcnow(),
            "processed_lines": 0,
            "total_records": 0,
            "error_count": 0
        }
        
        result = self.import_status_collection.update_one(
            {"file_path": str(file_path)},
            {"$set": status_doc},
            upsert=True
        )
        return file_hash

    def _mark_file_completed(self, file_path: Path, total_records: int, error_count: int):
        """标记文件导入完成"""
        self.import_status_collection.update_one(
            {"file_path": str(file_path)},
            {"$set": {
                "status": "completed",
                "end_time": datetime.utcnow(),
                "total_records": total_records,
                "error_count": error_count
            }}
        )

    def _mark_file_failed(self, file_path: Path, error_msg: str):
        """标记文件导入失败"""
        self.import_status_collection.update_one(
            {"file_path": str(file_path)},
            {"$set": {
                "status": "failed",
                "end_time": datetime.utcnow(),
                "error_message": error_msg
            }}
        )

    def _parse_json_line(self, line: str) -> List[Dict[str, Any]]:
        """解析一行中的JSON数据，可能包含多个JSON对象"""
        line = line.strip()
        if not line:
            return []

        json_objects = []

        # 尝试直接解析整行
        try:
            obj = json.loads(line)
            json_objects.append(obj)
            return json_objects
        except json.JSONDecodeError:
            pass

        # 如果直接解析失败，尝试分割多个JSON对象
        # 简单的启发式方法：查找 }{ 模式
        parts = []
        brace_count = 0
        current_part = ""

        for char in line:
            current_part += char
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    parts.append(current_part)
                    current_part = ""

        # 解析每个部分
        for part in parts:
            part = part.strip()
            if part:
                try:
                    obj = json.loads(part)
                    json_objects.append(obj)
                except json.JSONDecodeError as e:
                    self.logger.warning(f"无法解析JSON: {part[:100]}... 错误: {e}")

        return json_objects

    def _ensure_collection_index(self, collection_name: str):
        """确保集合有必要的索引"""
        collection = self.db[collection_name]
        try:
            # 为t_log_id创建唯一索引
            collection.create_index("t_log_id", unique=True)
            # 为时间戳创建索引
            collection.create_index("t_utc_timestamp")
        except Exception as e:
            self.logger.debug(f"索引可能已存在: {e}")

    def _batch_insert_records(self, records_by_event: Dict[str, List[Dict[str, Any]]]):
        """批量插入记录到对应的集合"""
        total_inserted = 0
        total_errors = 0

        for event_name, records in records_by_event.items():
            if not records:
                continue

            collection_name = event_name
            collection = self.db[collection_name]

            # 确保索引存在
            self._ensure_collection_index(collection_name)

            try:
                # 使用upsert避免重复插入
                operations = []
                for record in records:
                    operations.append(
                        UpdateOne(
                            {"t_log_id": record["t_log_id"]},
                            {"$setOnInsert": record},
                            upsert=True
                        )
                    )

                if operations:
                    result = collection.bulk_write(operations, ordered=False)
                    inserted_count = result.upserted_count
                    total_inserted += inserted_count

                    self.logger.info(f"集合 {collection_name}: 插入 {inserted_count} 条记录")

            except BulkWriteError as e:
                # 处理批量写入错误
                inserted_count = e.details.get('nUpserted', 0)
                error_count = len(e.details.get('writeErrors', []))
                total_inserted += inserted_count
                total_errors += error_count

                self.logger.warning(f"集合 {collection_name}: 插入 {inserted_count} 条，错误 {error_count} 条")

            except Exception as e:
                total_errors += len(records)
                self.logger.error(f"集合 {collection_name} 批量插入失败: {e}")

        return total_inserted, total_errors

    def import_file(self, file_path: Path) -> bool:
        """导入单个文件"""
        self.logger.info(f"开始导入文件: {file_path}")

        # 检查文件是否已经导入
        if self._is_file_imported(file_path):
            self.logger.info(f"文件已导入，跳过: {file_path}")
            return True

        # 标记开始导入
        file_hash = self._mark_file_importing(file_path)

        total_records = 0
        total_errors = 0
        processed_lines = 0

        try:
            # 按批次处理文件
            records_by_event = {}

            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    processed_lines += 1

                    # 解析JSON对象
                    json_objects = self._parse_json_line(line)

                    for json_obj in json_objects:
                        if not isinstance(json_obj, dict):
                            continue

                        # 检查必要字段
                        if 't_log_id' not in json_obj or 't_event_name' not in json_obj:
                            self.logger.warning(f"行 {line_num}: 缺少必要字段 t_log_id 或 t_event_name")
                            total_errors += 1
                            continue

                        event_name = json_obj['t_event_name']

                        # 按事件名分组
                        if event_name not in records_by_event:
                            records_by_event[event_name] = []

                        records_by_event[event_name].append(json_obj)
                        total_records += 1

                    # 达到批次大小时进行插入
                    current_batch_size = sum(len(records) for records in records_by_event.values())
                    if current_batch_size >= self.batch_size:
                        inserted, errors = self._batch_insert_records(records_by_event)
                        total_errors += errors

                        # 清空缓存
                        records_by_event = {}

                        # 更新进度
                        self.import_status_collection.update_one(
                            {"file_path": str(file_path)},
                            {"$set": {"processed_lines": processed_lines}}
                        )

                        self.logger.info(f"已处理 {processed_lines} 行，插入 {inserted} 条记录")

            # 处理剩余的记录
            if records_by_event:
                inserted, errors = self._batch_insert_records(records_by_event)
                total_errors += errors

            # 标记完成
            self._mark_file_completed(file_path, total_records, total_errors)

            self.logger.info(f"文件导入完成: {file_path}")
            self.logger.info(f"总记录数: {total_records}, 错误数: {total_errors}")

            return True

        except Exception as e:
            error_msg = f"导入文件失败: {e}"
            self.logger.error(error_msg)
            self._mark_file_failed(file_path, error_msg)
            return False

    def import_all_files(self):
        """导入所有.action.log文件"""
        if not self.log_dir.exists():
            self.logger.error(f"日志目录不存在: {self.log_dir}")
            return

        # 查找所有.action.log文件
        log_files = list(self.log_dir.glob("*.action.log"))

        if not log_files:
            self.logger.warning(f"在目录 {self.log_dir} 中未找到 .action.log 文件")
            return

        self.logger.info(f"找到 {len(log_files)} 个日志文件")

        success_count = 0
        for file_path in log_files:
            if self.import_file(file_path):
                success_count += 1

        self.logger.info(f"导入完成: 成功 {success_count}/{len(log_files)} 个文件")

    def get_import_status(self) -> List[Dict[str, Any]]:
        """获取导入状态"""
        return list(self.import_status_collection.find().sort("start_time", -1))

    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()


def main():
    """主程序入口"""
    import argparse

    parser = argparse.ArgumentParser(description='MongoDB日志导入工具')
    parser.add_argument('--mongo-uri', default='mongodb://localhost:27017',
                       help='MongoDB连接字符串 (默认: mongodb://localhost:27017)')
    parser.add_argument('--database', default='jkx_event_log',
                       help='数据库名称 (默认: jkx_event_log)')
    parser.add_argument('--log-dir', default=r'H:\jkxlogs',
                       help='日志文件目录 (默认: H:\\jkxlogs)')
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='批量插入大小 (默认: 1000)')
    parser.add_argument('--status', action='store_true',
                       help='显示导入状态')

    args = parser.parse_args()

    # 创建导入器
    importer = LogImporter(
        mongo_uri=args.mongo_uri,
        database_name=args.database,
        log_dir=args.log_dir,
        batch_size=args.batch_size
    )

    try:
        if args.status:
            # 显示导入状态
            status_list = importer.get_import_status()
            print("\n=== 导入状态 ===")
            for status in status_list:
                print(f"文件: {status['file_path']}")
                print(f"状态: {status['status']}")
                print(f"开始时间: {status.get('start_time', 'N/A')}")
                print(f"总记录数: {status.get('total_records', 0)}")
                print(f"错误数: {status.get('error_count', 0)}")
                print("-" * 50)
        else:
            # 执行导入
            importer.import_all_files()

    except KeyboardInterrupt:
        print("\n用户中断导入")
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
    finally:
        importer.close()


if __name__ == "__main__":
    main()
