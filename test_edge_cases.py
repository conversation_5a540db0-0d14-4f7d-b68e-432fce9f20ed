#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析的边缘情况
"""

import tempfile
from pathlib import Path
from log_importer import LogImporter

def test_edge_cases():
    """测试各种边缘情况"""
    
    print("=== JSON解析边缘情况测试 ===\n")
    
    # 创建临时目录和测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_file = temp_path / "edge_cases.action.log"
        
        # 创建包含各种边缘情况的测试数据
        test_lines = [
            # 1. 正常的单个JSON
            '{"t_log_id":"normal_001","t_event_name":"normal","value":1}',
            
            # 2. 两个连续JSON，无空格
            '{"t_log_id":"concat_001","t_event_name":"concat1"}{"t_log_id":"concat_002","t_event_name":"concat2"}',
            
            # 3. 复杂嵌套JSON
            '{"t_log_id":"nested_001","t_event_name":"nested","data":{"level1":{"level2":{"level3":{"value":42}}},"array":[{"item":1},{"item":2}]}}',
            
            # 4. 包含特殊字符的JSON
            '{"t_log_id":"special_001","t_event_name":"special","text":"包含中文和特殊字符!@#$%^&*()","unicode":"🎮🎯🎲"}',
            
            # 5. 带前缀文本的JSON
            'prefix text {"t_log_id":"prefix_001","t_event_name":"prefixed"}',
            
            # 6. 多个JSON之间有各种分隔符
            '{"t_log_id":"sep_001","value":1} \t\n {"t_log_id":"sep_002","value":2}',
            
            # 7. JSON中包含转义字符
            '{"t_log_id":"escape_001","t_event_name":"escape","text":"包含\\"引号\\"和\\n换行符"}',
            
            # 8. 大数字和浮点数
            '{"t_log_id":"number_001","t_event_name":"numbers","big_int":9223372036854775807,"float":3.14159265359,"scientific":1.23e-10}',
            
            # 9. 布尔值和null
            '{"t_log_id":"bool_001","t_event_name":"booleans","is_true":true,"is_false":false,"is_null":null}',
            
            # 10. 空对象和空数组
            '{"t_log_id":"empty_001","t_event_name":"empty","empty_obj":{},"empty_array":[]}',
            
            # 11. 深度嵌套数组
            '{"t_log_id":"array_001","t_event_name":"arrays","matrix":[[1,2,3],[4,5,6],[7,8,9]],"nested_arrays":[[[1]]]}',
            
            # 12. 混合类型的复杂JSON
            '{"t_log_id":"complex_001","t_event_name":"complex","mixed":{"string":"text","number":123,"boolean":true,"null_value":null,"array":[1,"two",3.0,true,null],"object":{"nested":"value"}}}',
            
            # 13. 包含URL和路径的JSON
            '{"t_log_id":"url_001","t_event_name":"urls","url":"https://example.com/path?param=value","file_path":"C:\\\\Users\\\\<USER>\\\\file.txt"}',
            
            # 14. 空行
            '',
            
            # 15. 只有空格的行
            '   \t   ',
            
            # 16. 无效JSON（应该被跳过）
            '{"invalid": json without quotes}',
            
            # 17. 不完整的JSON（应该被跳过）
            '{"incomplete":',
            
            # 18. 多个JSON，其中一个无效
            '{"t_log_id":"valid_001","t_event_name":"valid"} {"invalid": bad} {"t_log_id":"valid_002","t_event_name":"valid"}',
        ]
        
        # 写入测试文件
        with open(test_file, 'w', encoding='utf-8') as f:
            for line in test_lines:
                f.write(line + '\n')
        
        print(f"创建测试文件: {test_file}")
        print(f"包含 {len(test_lines)} 行测试数据\n")
        
        # 创建导入器
        importer = LogImporter(
            database_name="test_edge_cases",
            log_dir=str(temp_path),
            batch_size=10
        )
        
        try:
            # 导入文件
            print("开始导入测试文件...")
            success = importer.import_file(test_file)
            print(f"导入结果: {'成功' if success else '失败'}\n")
            
            # 查看导入状态
            status_list = importer.get_import_status()
            if status_list:
                status = status_list[0]
                print("=== 导入状态 ===")
                print(f"状态: {status['status']}")
                print(f"处理行数: {status.get('processed_lines', 0)}")
                print(f"总记录数: {status.get('total_records', 0)}")
                print(f"错误数: {status.get('error_count', 0)}\n")
            
            # 查看各个集合的记录数
            print("=== 各事件类型记录数 ===")
            collections = importer.db.list_collection_names()
            event_collections = [c for c in collections if not c.startswith('_')]
            
            total_records = 0
            for collection_name in sorted(event_collections):
                collection = importer.db[collection_name]
                count = collection.count_documents({})
                total_records += count
                print(f"{collection_name}: {count} 条记录")
                
                # 显示一个示例记录的关键信息
                sample = collection.find_one()
                if sample:
                    log_id = sample.get('t_log_id', 'N/A')
                    print(f"  示例记录ID: {log_id}")
            
            print(f"\n总记录数: {total_records}")
            
            # 验证特定记录
            print("\n=== 验证特定记录 ===")
            
            # 检查复杂嵌套记录
            nested_collection = importer.db.get_collection("nested")
            nested_record = nested_collection.find_one({"t_log_id": "nested_001"})
            if nested_record:
                print("✓ 复杂嵌套JSON解析正确")
                print(f"  嵌套值: {nested_record['data']['level1']['level2']['level3']['value']}")
            else:
                print("✗ 复杂嵌套JSON解析失败")
            
            # 检查特殊字符记录
            special_collection = importer.db.get_collection("special")
            special_record = special_collection.find_one({"t_log_id": "special_001"})
            if special_record:
                print("✓ 特殊字符JSON解析正确")
                print(f"  中文文本: {special_record['text']}")
                print(f"  Unicode: {special_record['unicode']}")
            else:
                print("✗ 特殊字符JSON解析失败")
            
            # 检查连续JSON记录
            concat_collection = importer.db.get_collection("concat1")
            concat_count = concat_collection.count_documents({})
            if concat_count > 0:
                print("✓ 连续JSON解析正确")
            else:
                print("✗ 连续JSON解析失败")
                
        except Exception as e:
            print(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理测试数据
            try:
                importer.client.drop_database("test_edge_cases")
                print("\n✓ 测试数据已清理")
            except:
                pass
            importer.close()

if __name__ == "__main__":
    test_edge_cases()
