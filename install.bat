@echo off
echo 安装MongoDB日志导入工具依赖...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo 安装Python依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 安装完成！
echo.
echo 使用方法:
echo   python run_import.py          - 导入所有日志文件
echo   python log_importer.py --help - 查看详细参数
echo   python test_current_log.py    - 测试当前目录中的日志文件
echo.
pause
