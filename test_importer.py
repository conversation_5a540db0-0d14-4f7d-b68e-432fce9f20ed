#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证日志导入工具的功能
"""

import json
import tempfile
from pathlib import Path
from log_importer import LogImporter


def create_test_log_file(file_path: Path, num_records: int = 10):
    """创建测试日志文件"""
    sample_data = {
        "t_log_id": "test_log_id_",
        "t_account_id": "google_613805726507314856",
        "t_tour_indicate": 1,
        "t_role_id": "********",
        "t_utc_timestamp": "2025-01-01T01:00:10.*********+08:00",
        "t_zone_id": 2012,
        "t_event_name": "test_event",
        "t_role_name": "测试角色",
        "t_role_level": 307,
        "property": {"test_key": "test_value"}
    }
    
    with open(file_path, 'w', encoding='utf-8') as f:
        for i in range(num_records):
            # 创建不同的事件类型
            data = sample_data.copy()
            data["t_log_id"] = f"test_log_id_{i:06d}"
            data["t_event_name"] = f"test_event_{i % 3}"  # 创建3种不同的事件类型
            data["property"]["record_id"] = i
            
            f.write(json.dumps(data, ensure_ascii=False) + '\n')
    
    print(f"创建测试文件: {file_path}, 包含 {num_records} 条记录")


def test_basic_import():
    """测试基本导入功能"""
    print("=== 测试基本导入功能 ===")
    
    # 创建临时目录和测试文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_file = temp_path / "test.action.log"
        
        # 创建测试数据
        create_test_log_file(test_file, 50)
        
        # 创建导入器 (使用测试数据库)
        importer = LogImporter(
            database_name="test_jkx_event_log",
            log_dir=str(temp_path),
            batch_size=10
        )
        
        try:
            # 执行导入
            success = importer.import_file(test_file)
            print(f"导入结果: {'成功' if success else '失败'}")
            
            # 检查导入状态
            status_list = importer.get_import_status()
            for status in status_list:
                print(f"文件: {Path(status['file_path']).name}")
                print(f"状态: {status['status']}")
                print(f"总记录数: {status.get('total_records', 0)}")
                print(f"错误数: {status.get('error_count', 0)}")
            
            # 验证数据是否正确插入
            for i in range(3):
                collection_name = f"test_event_{i}"
                collection = importer.db[collection_name]
                count = collection.count_documents({})
                print(f"集合 {collection_name} 记录数: {count}")
            
        finally:
            # 清理测试数据
            importer.db.drop_collection("test_event_0")
            importer.db.drop_collection("test_event_1") 
            importer.db.drop_collection("test_event_2")
            importer.db.drop_collection("_import_status")
            importer.close()


def test_duplicate_import():
    """测试重复导入防护"""
    print("\n=== 测试重复导入防护 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_file = temp_path / "test_dup.action.log"
        
        # 创建测试数据
        create_test_log_file(test_file, 20)
        
        importer = LogImporter(
            database_name="test_jkx_event_log",
            log_dir=str(temp_path),
            batch_size=5
        )
        
        try:
            # 第一次导入
            print("第一次导入...")
            success1 = importer.import_file(test_file)
            
            # 检查记录数
            collection = importer.db["test_event_0"]
            count1 = collection.count_documents({})
            print(f"第一次导入后记录数: {count1}")
            
            # 第二次导入同一文件
            print("第二次导入同一文件...")
            success2 = importer.import_file(test_file)
            
            # 检查记录数是否没有增加
            count2 = collection.count_documents({})
            print(f"第二次导入后记录数: {count2}")
            
            if count1 == count2:
                print("✓ 重复导入防护正常工作")
            else:
                print("✗ 重复导入防护失败")
                
        finally:
            # 清理测试数据
            importer.db.drop_collection("test_event_0")
            importer.db.drop_collection("test_event_1")
            importer.db.drop_collection("test_event_2")
            importer.db.drop_collection("_import_status")
            importer.close()


def test_multi_json_line():
    """测试多JSON对象行解析"""
    print("\n=== 测试多JSON对象行解析 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_file = temp_path / "test_multi.action.log"
        
        # 创建包含多个JSON对象的行
        data1 = {"t_log_id": "multi_1", "t_event_name": "multi_test", "value": 1}
        data2 = {"t_log_id": "multi_2", "t_event_name": "multi_test", "value": 2}
        
        with open(test_file, 'w', encoding='utf-8') as f:
            # 写入包含两个JSON对象的行
            line = json.dumps(data1, ensure_ascii=False) + json.dumps(data2, ensure_ascii=False)
            f.write(line + '\n')
        
        importer = LogImporter(
            database_name="test_jkx_event_log",
            log_dir=str(temp_path)
        )
        
        try:
            success = importer.import_file(test_file)
            
            # 检查是否正确解析了两个对象
            collection = importer.db["multi_test"]
            count = collection.count_documents({})
            print(f"多JSON行解析结果: {count} 条记录")
            
            if count == 2:
                print("✓ 多JSON对象解析正常")
            else:
                print("✗ 多JSON对象解析失败")
                
        finally:
            importer.db.drop_collection("multi_test")
            importer.db.drop_collection("_import_status")
            importer.close()


if __name__ == "__main__":
    print("开始测试MongoDB日志导入工具...")
    
    try:
        test_basic_import()
        test_duplicate_import()
        test_multi_json_line()
        print("\n所有测试完成!")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
