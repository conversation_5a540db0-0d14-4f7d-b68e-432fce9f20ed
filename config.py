#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# MongoDB配置
MONGO_URI = "mongodb://localhost:27017"
DATABASE_NAME = "jkx_event_log"

# 日志文件配置
LOG_DIR = r"H:\jkxlogs"
LOG_FILE_PATTERN = "*.action.log"

# 导入配置
BATCH_SIZE = 1000  # 批量插入大小
ENCODING = "utf-8"  # 文件编码

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "import.log"

# 字段配置
REQUIRED_FIELDS = ["t_log_id", "t_event_name"]
UNIQUE_FIELD = "t_log_id"
EVENT_NAME_FIELD = "t_event_name"
TIMESTAMP_FIELD = "t_utc_timestamp"

# 状态表名称
STATUS_COLLECTION = "_import_status"
