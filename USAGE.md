# 使用指南

## 快速开始

### 1. 安装
```bash
# Windows
install.bat

# Linux/Mac  
./install.sh
```

### 2. 配置
编辑 `config.py` 文件，设置你的MongoDB连接和日志目录：
```python
MONGO_URI = "mongodb://localhost:27017"
DATABASE_NAME = "jkx_event_log"
LOG_DIR = r"H:\jkxlogs"  # 修改为你的日志目录
```

### 3. 测试
```bash
# 测试当前目录中的示例日志文件
python test_current_log.py
```

### 4. 正式导入
```bash
# 导入所有日志文件
python run_import.py
```

## 高级用法

### 命令行参数
```bash
# 自定义参数导入
python log_importer.py --log-dir "D:\logs" --batch-size 2000

# 查看导入状态
python log_importer.py --status

# 查看帮助
python log_importer.py --help
```

### 编程接口
```python
from log_importer import LogImporter

importer = LogImporter(
    mongo_uri="mongodb://localhost:27017",
    database_name="jkx_event_log",
    log_dir=r"H:\jkxlogs",
    batch_size=1000
)

try:
    # 导入所有文件
    importer.import_all_files()
    
    # 或导入单个文件
    from pathlib import Path
    importer.import_file(Path("path/to/file.action.log"))
    
    # 查看状态
    status = importer.get_import_status()
    for s in status:
        print(f"文件: {s['file_path']}, 状态: {s['status']}")
        
finally:
    importer.close()
```

## 数据结构

### 输入数据格式
每行一个或多个JSON对象，必须包含：
- `t_log_id`: 唯一标识符
- `t_event_name`: 事件名称（用于分表）

### 输出数据库结构
- 根据 `t_event_name` 创建不同的集合
- 每个集合自动创建 `t_log_id` 唯一索引
- `_import_status` 集合记录导入状态

## 性能优化建议

1. **批量大小**: 根据内存情况调整 `batch_size`，默认1000
2. **并发**: 目前是单线程，大量文件可考虑多进程
3. **索引**: 程序自动创建必要索引，无需手动创建
4. **磁盘空间**: 确保有足够空间存储数据和索引

## 故障排除

### 常见问题

1. **连接失败**
   - 检查MongoDB是否运行
   - 验证连接字符串是否正确

2. **文件读取失败**
   - 检查文件路径是否正确
   - 确认文件编码为UTF-8

3. **JSON解析错误**
   - 检查日志文件格式
   - 查看 `import.log` 了解详细错误

4. **重复导入**
   - 程序会自动跳过已导入的文件
   - 基于文件哈希值判断

### 日志文件
查看 `import.log` 文件获取详细的导入过程和错误信息。

## 监控和维护

### 查看导入状态
```python
from log_importer import LogImporter

importer = LogImporter()
status_list = importer.get_import_status()

for status in status_list:
    print(f"文件: {status['file_path']}")
    print(f"状态: {status['status']}")
    print(f"记录数: {status.get('total_records', 0)}")
    print(f"错误数: {status.get('error_count', 0)}")
```

### 数据验证
```python
# 检查各集合记录数
collections = importer.db.list_collection_names()
for collection_name in collections:
    if not collection_name.startswith('_'):
        count = importer.db[collection_name].count_documents({})
        print(f"{collection_name}: {count} 条记录")
```
