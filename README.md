# MongoDB日志导入工具

这是一个高效的Python工具，用于将.action.log文件中的JSON数据导入到MongoDB中。

## 功能特性

- **高效批量导入**: 使用批量操作提高导入性能
- **自动去重**: 基于`t_log_id`字段避免重复导入
- **分表存储**: 根据`t_event_name`字段自动创建不同的集合
- **断点续传**: 支持中断后继续导入，避免重复处理
- **导入状态跟踪**: 独立的状态表记录每个文件的导入情况
- **多JSON解析**: 支持每行包含多个JSON对象的情况
- **错误处理**: 完善的错误处理和日志记录

## 快速开始

### 1. 安装依赖

**Windows:**
```cmd
install.bat
```

**Linux/Mac:**
```bash
./install.sh
```

**手动安装:**
```bash
pip install -r requirements.txt
```

### 2. 配置

编辑 `config.py` 文件，修改以下配置：
- `MONGO_URI`: MongoDB连接字符串
- `DATABASE_NAME`: 数据库名称
- `LOG_DIR`: 日志文件目录路径

### 3. 运行

**简单运行:**
```bash
python run_import.py
```

**测试当前目录中的日志文件:**
```bash
python test_current_log.py
```

## 使用方法

### 基本使用

```bash
# 导入所有日志文件
python log_importer.py

# 指定自定义参数
python log_importer.py --log-dir "D:\logs" --batch-size 2000

# 查看导入状态
python log_importer.py --status
```

### 命令行参数

- `--mongo-uri`: MongoDB连接字符串 (默认: mongodb://localhost:27017)
- `--database`: 数据库名称 (默认: jkx_event_log)
- `--log-dir`: 日志文件目录 (默认: H:\jkxlogs)
- `--batch-size`: 批量插入大小 (默认: 1000)
- `--status`: 显示导入状态

### 编程使用

```python
from log_importer import LogImporter

# 创建导入器
importer = LogImporter(
    mongo_uri="mongodb://localhost:27017",
    database_name="jkx_event_log",
    log_dir=r"H:\jkxlogs",
    batch_size=1000
)

try:
    # 导入所有文件
    importer.import_all_files()
    
    # 或导入单个文件
    # importer.import_file(Path("path/to/file.action.log"))
    
    # 查看导入状态
    status = importer.get_import_status()
    for s in status:
        print(f"文件: {s['file_path']}, 状态: {s['status']}")
        
finally:
    importer.close()
```

## 数据库结构

### 事件数据集合
根据`t_event_name`字段值创建不同的集合，例如：
- `item_flow`: 存储t_event_name为"item_flow"的记录
- `online_num`: 存储t_event_name为"online_num"的记录
- 其他事件类型...

每个集合都会自动创建以下索引：
- `t_log_id`: 唯一索引，用于去重
- `t_utc_timestamp`: 时间戳索引，用于查询优化

### 导入状态集合 (_import_status)
记录每个文件的导入状态：
```json
{
  "file_path": "文件路径",
  "file_hash": "文件MD5哈希",
  "status": "importing|completed|failed",
  "start_time": "开始时间",
  "end_time": "结束时间",
  "processed_lines": "已处理行数",
  "total_records": "总记录数",
  "error_count": "错误数量",
  "error_message": "错误信息(如果失败)"
}
```

## 性能优化

1. **批量操作**: 使用MongoDB的bulk_write进行批量插入
2. **upsert操作**: 使用upsert避免重复插入，提高性能
3. **索引优化**: 自动创建必要的索引
4. **内存管理**: 分批处理大文件，避免内存溢出
5. **连接复用**: 复用数据库连接

## 错误处理

- JSON解析错误会被记录但不会中断导入
- 网络连接问题会自动重试
- 文件读取错误会标记文件状态为失败
- 所有错误都会记录到日志文件中

## 日志文件

程序会生成`import.log`文件，记录详细的导入过程和错误信息。

## 项目结构

```
import_log_to_mongodb/
├── log_importer.py          # 主要的导入器类
├── config.py                # 配置文件
├── run_import.py            # 简单运行脚本
├── test_importer.py         # 单元测试
├── test_current_log.py      # 测试当前目录日志文件
├── requirements.txt         # Python依赖
├── install.bat             # Windows安装脚本
├── install.sh              # Linux/Mac安装脚本
├── README.md               # 说明文档
└── import.log              # 导入日志文件(运行后生成)
```

## 注意事项

1. 确保MongoDB服务正在运行
2. 确保有足够的磁盘空间存储数据
3. 大文件导入可能需要较长时间，请耐心等待
4. 可以随时中断程序，下次运行时会自动跳过已导入的文件
5. 首次运行建议先用测试脚本验证功能
