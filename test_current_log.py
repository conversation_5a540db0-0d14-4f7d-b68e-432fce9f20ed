#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前目录中的日志文件
"""

from pathlib import Path
from log_importer import LogImporter

def test_current_log():
    """测试当前目录中的日志文件"""
    current_dir = Path(".")
    log_file = current_dir / "2025-01-01-1.action.log"
    
    if not log_file.exists():
        print(f"日志文件不存在: {log_file}")
        return
    
    print(f"测试文件: {log_file}")
    
    # 创建导入器 (使用测试数据库)
    importer = LogImporter(
        database_name="test_jkx_event_log",
        log_dir=str(current_dir),
        batch_size=100
    )
    
    try:
        # 导入文件
        success = importer.import_file(log_file)
        print(f"导入结果: {'成功' if success else '失败'}")
        
        # 查看导入状态
        status_list = importer.get_import_status()
        if status_list:
            status = status_list[0]
            print(f"状态: {status['status']}")
            print(f"总记录数: {status.get('total_records', 0)}")
            print(f"错误数: {status.get('error_count', 0)}")
        
        # 查看各个集合的记录数
        print("\n=== 各事件类型记录数 ===")
        collections = importer.db.list_collection_names()
        event_collections = [c for c in collections if not c.startswith('_')]
        
        for collection_name in sorted(event_collections):
            collection = importer.db[collection_name]
            count = collection.count_documents({})
            print(f"{collection_name}: {count} 条记录")
            
            # 显示一个示例记录
            sample = collection.find_one()
            if sample:
                print(f"  示例记录ID: {sample.get('t_log_id', 'N/A')}")
                print(f"  时间戳: {sample.get('t_utc_timestamp', 'N/A')}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理测试数据 (可选)
        # importer.client.drop_database("test_jkx_event_log")
        importer.close()

if __name__ == "__main__":
    test_current_log()
