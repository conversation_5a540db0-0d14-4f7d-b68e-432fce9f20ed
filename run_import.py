#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的运行脚本
"""

from log_importer import LogImporter
import config

def main():
    """主函数"""
    print("=== MongoDB日志导入工具 ===")
    print(f"数据库: {config.DATABASE_NAME}")
    print(f"日志目录: {config.LOG_DIR}")
    print(f"批量大小: {config.BATCH_SIZE}")
    print()
    
    # 创建导入器
    importer = LogImporter(
        mongo_uri=config.MONGO_URI,
        database_name=config.DATABASE_NAME,
        log_dir=config.LOG_DIR,
        batch_size=config.BATCH_SIZE
    )
    
    try:
        # 执行导入
        print("开始导入...")
        importer.import_all_files()
        print("导入完成!")
        
        # 显示状态
        print("\n=== 导入状态 ===")
        status_list = importer.get_import_status()
        for status in status_list[:5]:  # 只显示最近5个
            print(f"文件: {status['file_path']}")
            print(f"状态: {status['status']}")
            print(f"记录数: {status.get('total_records', 0)}")
            print(f"错误数: {status.get('error_count', 0)}")
            print("-" * 40)
            
    except KeyboardInterrupt:
        print("\n用户中断导入")
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        importer.close()

if __name__ == "__main__":
    main()
